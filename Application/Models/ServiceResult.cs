namespace Application.Models;

public class ServiceResult<T>
{
    public bool IsSuccess { get; private set; }
    public T? Data { get; private set; }
    public string? ErrorMessage { get; private set; }
    public ServiceErrorType ErrorType { get; private set; }

    private ServiceResult(bool isSuccess, T? data, string? errorMessage, ServiceErrorType errorType)
    {
        IsSuccess = isSuccess;
        Data = data;
        ErrorMessage = errorMessage;
        ErrorType = errorType;
    }

    public static ServiceResult<T> Success(T data)
    {
        return new ServiceResult<T>(true, data, null, ServiceErrorType.None);
    }

    public static ServiceResult<T> Failure(string errorMessage, ServiceErrorType errorType = ServiceErrorType.General)
    {
        return new ServiceResult<T>(false, default, errorMessage, errorType);
    }

    public static ServiceResult<T> Conflict(string errorMessage)
    {
        return new ServiceResult<T>(false, default, errorMessage, ServiceErrorType.Conflict);
    }

    public static ServiceResult<T> NotFound(string errorMessage)
    {
        return new ServiceResult<T>(false, default, errorMessage, ServiceErrorType.NotFound);
    }

    public static ServiceResult<T> ValidationError(string errorMessage)
    {
        return new ServiceResult<T>(false, default, errorMessage, ServiceErrorType.Validation);
    }
}

public enum ServiceErrorType
{
    None,
    General,
    NotFound,
    Conflict,
    Validation
}
