using Application.DTOs;
using Application.Interfaces;
using Application.Models;
using Persistence.Entities;
using Persistence.Interfaces;

namespace Application.Services;

public class EventService : IEventService
{
    private readonly IEventRepository _eventRepository;

    public EventService(IEventRepository eventRepository)
    {
        _eventRepository = eventRepository;
    }

    public async Task<IEnumerable<EventDto>> GetAllEventsAsync()
    {
        var result = await _eventRepository.GetAllAsync();
        if (!result.IsSuccess || result.Data == null)
        {
            return Enumerable.Empty<EventDto>();
        }

        return result.Data.Select(MapToDto);
    }

    public async Task<IEnumerable<EventDto>> GetUpcomingEventsAsync()
    {
        var result = await _eventRepository.GetUpcomingEventsAsync();
        if (!result.IsSuccess || result.Data == null)
        {
            return Enumerable.Empty<EventDto>();
        }

        return result.Data.Select(MapToDto);
    }

    public async Task<EventDto?> GetEventByIdAsync(Guid id)
    {
        var result = await _eventRepository.GetByIdAsync(id);
        return result.IsSuccess && result.Data != null ? MapToDto(result.Data) : null;
    }

    public async Task<ServiceResult<EventDto>> CreateEventAsync(CreateEventDto createEventDto)
    {
        // Validate that end time is after start time
        if (createEventDto.EndTime <= createEventDto.StartTime)
        {
            return ServiceResult<EventDto>.ValidationError("End time must be after start time.");
        }

        // Check for booking conflicts
        var conflictResult = await _eventRepository.HasBookingConflictAsync(
            createEventDto.Location,
            createEventDto.LocationRoom,
            createEventDto.StartTime,
            createEventDto.EndTime
        );

        if (!conflictResult.IsSuccess)
        {
            return ServiceResult<EventDto>.Failure(
                $"Error checking for conflicts: {conflictResult.ErrorMessage}"
            );
        }

        if (conflictResult.Data == true)
        {
            return ServiceResult<EventDto>.Conflict(
                $"The location '{createEventDto.Location}' room '{createEventDto.LocationRoom}' is already booked for the specified time period."
            );
        }

        var eventEntity = new EventEntity
        {
            Title = createEventDto.Title,
            Description = createEventDto.Description,
            StartTime = createEventDto.StartTime,
            EndTime = createEventDto.EndTime,
            Location = createEventDto.Location,
            LocationRoom = createEventDto.LocationRoom,
        };

        var result = await _eventRepository.AddAsync(eventEntity);

        if (!result.IsSuccess || result.Data == null)
        {
            return ServiceResult<EventDto>.Failure($"Error creating event: {result.ErrorMessage}");
        }

        return ServiceResult<EventDto>.Success(MapToDto(result.Data));
    }

    public async Task<ServiceResult<EventDto>> UpdateEventAsync(
        Guid id,
        CreateEventDto updateEventDto
    )
    {
        // Validate that end time is after start time
        if (updateEventDto.EndTime <= updateEventDto.StartTime)
        {
            return ServiceResult<EventDto>.ValidationError("End time must be after start time.");
        }

        var existingResult = await _eventRepository.GetByIdAsync(id);
        if (!existingResult.IsSuccess || existingResult.Data == null)
        {
            return ServiceResult<EventDto>.NotFound("Event not found.");
        }

        // Check for booking conflicts (excluding the current event)
        var conflictResult = await _eventRepository.HasBookingConflictAsync(
            updateEventDto.Location,
            updateEventDto.LocationRoom,
            updateEventDto.StartTime,
            updateEventDto.EndTime,
            id // Exclude current event from conflict check
        );

        if (!conflictResult.IsSuccess)
        {
            return ServiceResult<EventDto>.Failure(
                $"Error checking for conflicts: {conflictResult.ErrorMessage}"
            );
        }

        if (conflictResult.Data == true)
        {
            return ServiceResult<EventDto>.Conflict(
                $"The location '{updateEventDto.Location}' room '{updateEventDto.LocationRoom}' is already booked for the specified time period."
            );
        }

        var eventEntity = existingResult.Data;
        eventEntity.Title = updateEventDto.Title;
        eventEntity.Description = updateEventDto.Description;
        eventEntity.StartTime = updateEventDto.StartTime;
        eventEntity.EndTime = updateEventDto.EndTime;
        eventEntity.Location = updateEventDto.Location;
        eventEntity.LocationRoom = updateEventDto.LocationRoom;

        var result = await _eventRepository.UpdateAsync(eventEntity);

        if (!result.IsSuccess || result.Data == null)
        {
            return ServiceResult<EventDto>.Failure($"Error updating event: {result.ErrorMessage}");
        }

        return ServiceResult<EventDto>.Success(MapToDto(result.Data));
    }

    public async Task<bool> DeleteEventAsync(Guid id)
    {
        var result = await _eventRepository.DeleteAsync(id);
        return result.IsSuccess && result.Data == true;
    }

    public async Task<IEnumerable<EventDto>> GetEventsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        var result = await _eventRepository.GetEventsByDateRangeAsync(startDate, endDate);
        if (!result.IsSuccess || result.Data == null)
        {
            return Enumerable.Empty<EventDto>();
        }

        return result.Data.Select(MapToDto);
    }

    private static EventDto MapToDto(EventEntity entity)
    {
        return new EventDto
        {
            Id = entity.Id,
            Title = entity.Title,
            Description = entity.Description,
            StartTime = entity.StartTime,
            EndTime = entity.EndTime,
            Location = entity.Location,
            LocationRoom = entity.LocationRoom,
        };
    }
}
