using Microsoft.EntityFrameworkCore;
using Persistence.Contexts;
using Persistence.Entities;
using Persistence.Repositories;

namespace EventService.Tests;

public class EventRepositoryTests : IDisposable
{
    private readonly DataContext _context;
    private readonly EventRepository _repository;

    public EventRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new DataContext(options);
        _repository = new EventRepository(_context);
    }

    [Fact]
    public async Task HasBookingConflictAsync_NoExistingEvents_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            startTime,
            endTime
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_ExactTimeOverlap_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        // Create existing event with exact same time
        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            startTime,
            endTime
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_PartialOverlap_NewEventStartsBeforeExistingEnds_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 10:00 - 12:00
        var existingStart = DateTime.UtcNow.AddHours(10);
        var existingEnd = DateTime.UtcNow.AddHours(12);

        // New event: 11:00 - 13:00 (overlaps with existing)
        var newStart = DateTime.UtcNow.AddHours(11);
        var newEnd = DateTime.UtcNow.AddHours(13);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_PartialOverlap_NewEventEndsAfterExistingStarts_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 11:00 - 13:00
        var existingStart = DateTime.UtcNow.AddHours(11);
        var existingEnd = DateTime.UtcNow.AddHours(13);

        // New event: 10:00 - 12:00 (overlaps with existing)
        var newStart = DateTime.UtcNow.AddHours(10);
        var newEnd = DateTime.UtcNow.AddHours(12);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_NewEventCompletelyInsideExisting_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 10:00 - 14:00
        var existingStart = DateTime.UtcNow.AddHours(10);
        var existingEnd = DateTime.UtcNow.AddHours(14);

        // New event: 11:00 - 13:00 (completely inside existing)
        var newStart = DateTime.UtcNow.AddHours(11);
        var newEnd = DateTime.UtcNow.AddHours(13);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_NewEventCompletelyContainsExisting_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 11:00 - 13:00
        var existingStart = DateTime.UtcNow.AddHours(11);
        var existingEnd = DateTime.UtcNow.AddHours(13);

        // New event: 10:00 - 14:00 (completely contains existing)
        var newStart = DateTime.UtcNow.AddHours(10);
        var newEnd = DateTime.UtcNow.AddHours(14);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_NoOverlap_NewEventAfterExisting_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 10:00 - 12:00
        var existingStart = DateTime.UtcNow.AddHours(10);
        var existingEnd = DateTime.UtcNow.AddHours(12);

        // New event: 13:00 - 15:00 (after existing)
        var newStart = DateTime.UtcNow.AddHours(13);
        var newEnd = DateTime.UtcNow.AddHours(15);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_NoOverlap_NewEventBeforeExisting_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Existing event: 13:00 - 15:00
        var existingStart = DateTime.UtcNow.AddHours(13);
        var existingEnd = DateTime.UtcNow.AddHours(15);

        // New event: 10:00 - 12:00 (before existing)
        var newStart = DateTime.UtcNow.AddHours(10);
        var newEnd = DateTime.UtcNow.AddHours(12);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_AdjacentEvents_NewEventEndsWhenExistingStarts_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";

        // Use fixed times to avoid precision issues
        var baseTime = new DateTime(2025, 1, 1, 10, 0, 0, DateTimeKind.Utc);

        // Existing event: 12:00 - 14:00
        var existingStart = baseTime.AddHours(2);
        var existingEnd = baseTime.AddHours(4);

        // New event: 10:00 - 12:00 (ends exactly when existing starts)
        var newStart = baseTime;
        var newEnd = baseTime.AddHours(2);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = existingStart,
            EndTime = existingEnd,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            newStart,
            newEnd
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_DifferentLocation_SameTimeAndRoom_ReturnsFalse()
    {
        // Arrange
        var existingLocation = "Gym A";
        var newLocation = "Gym B";
        var locationRoom = "Room 1";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = existingLocation,
            LocationRoom = locationRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            newLocation,
            locationRoom,
            startTime,
            endTime
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_SameLocation_DifferentRoom_SameTime_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var existingRoom = "Room 1";
        var newRoom = "Room 2";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = existingRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.HasBookingConflictAsync(
            location,
            newRoom,
            startTime,
            endTime
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task HasBookingConflictAsync_WithExcludeEventId_ExcludesSpecifiedEvent_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        var existingEvent = new EventEntity
        {
            Title = "Existing Event",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddAsync(existingEvent);
        await _context.SaveChangesAsync();

        // Act - exclude the existing event from conflict check
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            startTime,
            endTime,
            existingEvent.Id
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data); // Should return false because we excluded the conflicting event
    }

    [Fact]
    public async Task HasBookingConflictAsync_WithExcludeEventId_StillDetectsOtherConflicts_ReturnsTrue()
    {
        // Arrange
        var location = "Gym A";
        var locationRoom = "Room 1";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        // Create two events with same time/location
        var event1 = new EventEntity
        {
            Title = "Event 1",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        var event2 = new EventEntity
        {
            Title = "Event 2",
            Description = "Test",
            Location = location,
            LocationRoom = locationRoom,
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddRangeAsync(event1, event2);
        await _context.SaveChangesAsync();

        // Act - exclude event1 but event2 should still cause conflict
        var result = await _repository.HasBookingConflictAsync(
            location,
            locationRoom,
            startTime,
            endTime,
            event1.Id
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Data); // Should return true because event2 still conflicts
    }

    [Fact]
    public async Task HasBookingConflictAsync_MultipleEventsInDifferentRooms_NoConflict_ReturnsFalse()
    {
        // Arrange
        var location = "Gym A";
        var startTime = DateTime.UtcNow.AddHours(1);
        var endTime = DateTime.UtcNow.AddHours(2);

        // Create events in different rooms
        var event1 = new EventEntity
        {
            Title = "Event 1",
            Description = "Test",
            Location = location,
            LocationRoom = "Room 1",
            StartTime = startTime,
            EndTime = endTime,
        };

        var event2 = new EventEntity
        {
            Title = "Event 2",
            Description = "Test",
            Location = location,
            LocationRoom = "Room 2",
            StartTime = startTime,
            EndTime = endTime,
        };

        await _context.Events.AddRangeAsync(event1, event2);
        await _context.SaveChangesAsync();

        // Act - check for conflict in Room 3
        var result = await _repository.HasBookingConflictAsync(
            location,
            "Room 3",
            startTime,
            endTime
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Data);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
