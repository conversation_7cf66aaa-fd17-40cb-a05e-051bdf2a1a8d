using Application.DTOs;
using Application.Models;
using Application.Services;
using Moq;
using Persistence.Entities;
using Persistence.Interfaces;
using Persistence.Models;

namespace EventService.Tests;

public class EventServiceTests
{
    private readonly Mock<IEventRepository> _mockRepository;
    private readonly Application.Services.EventService _eventService;

    public EventServiceTests()
    {
        _mockRepository = new Mock<IEventRepository>();
        _eventService = new Application.Services.EventService(_mockRepository.Object);
    }

    [Fact]
    public async Task CreateEventAsync_ValidEvent_NoConflict_ReturnsSuccess()
    {
        // Arrange
        var createEventDto = new CreateEventDto
        {
            Title = "Test Event",
            Description = "Test Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(1),
            EndTime = DateTime.UtcNow.AddHours(2),
        };

        _mockRepository
            .Setup(r =>
                r.HasBookingConflictAsync(
                    createEventDto.Location,
                    createEventDto.LocationRoom,
                    createEventDto.StartTime,
                    createEventDto.EndTime,
                    null
                )
            )
            .ReturnsAsync(RepositoryResult<bool>.Success(false));

        var savedEvent = new EventEntity
        {
            Id = Guid.NewGuid(),
            Title = createEventDto.Title,
            Description = createEventDto.Description,
            Location = createEventDto.Location,
            LocationRoom = createEventDto.LocationRoom,
            StartTime = createEventDto.StartTime,
            EndTime = createEventDto.EndTime,
        };

        _mockRepository
            .Setup(r => r.AddAsync(It.IsAny<EventEntity>()))
            .ReturnsAsync(RepositoryResult<EventEntity>.Success(savedEvent));

        // Act
        var result = await _eventService.CreateEventAsync(createEventDto);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Data);
        Assert.Equal(createEventDto.Title, result.Data.Title);
        Assert.Equal(createEventDto.Location, result.Data.Location);
        Assert.Equal(createEventDto.LocationRoom, result.Data.LocationRoom);
    }

    [Fact]
    public async Task CreateEventAsync_EndTimeBeforeStartTime_ReturnsValidationError()
    {
        // Arrange
        var createEventDto = new CreateEventDto
        {
            Title = "Test Event",
            Description = "Test Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(2),
            EndTime = DateTime.UtcNow.AddHours(1), // End before start
        };

        // Act
        var result = await _eventService.CreateEventAsync(createEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.Validation, result.ErrorType);
        Assert.Equal("End time must be after start time.", result.ErrorMessage);
    }

    [Fact]
    public async Task CreateEventAsync_BookingConflict_ReturnsConflictError()
    {
        // Arrange
        var createEventDto = new CreateEventDto
        {
            Title = "Test Event",
            Description = "Test Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(1),
            EndTime = DateTime.UtcNow.AddHours(2),
        };

        _mockRepository
            .Setup(r =>
                r.HasBookingConflictAsync(
                    createEventDto.Location,
                    createEventDto.LocationRoom,
                    createEventDto.StartTime,
                    createEventDto.EndTime,
                    null
                )
            )
            .ReturnsAsync(RepositoryResult<bool>.Success(true)); // Conflict exists

        // Act
        var result = await _eventService.CreateEventAsync(createEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.Conflict, result.ErrorType);
        Assert.Contains("already booked", result.ErrorMessage);
        Assert.Contains(createEventDto.Location, result.ErrorMessage);
        Assert.Contains(createEventDto.LocationRoom, result.ErrorMessage);
    }

    [Fact]
    public async Task CreateEventAsync_ConflictCheckFails_ReturnsGeneralError()
    {
        // Arrange
        var createEventDto = new CreateEventDto
        {
            Title = "Test Event",
            Description = "Test Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(1),
            EndTime = DateTime.UtcNow.AddHours(2),
        };

        _mockRepository
            .Setup(r =>
                r.HasBookingConflictAsync(
                    createEventDto.Location,
                    createEventDto.LocationRoom,
                    createEventDto.StartTime,
                    createEventDto.EndTime,
                    null
                )
            )
            .ReturnsAsync(RepositoryResult<bool>.Failure("Database error"));

        // Act
        var result = await _eventService.CreateEventAsync(createEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.General, result.ErrorType);
        Assert.Contains("Error checking for conflicts", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateEventAsync_ValidUpdate_NoConflict_ReturnsSuccess()
    {
        // Arrange
        var eventId = Guid.NewGuid();
        var updateEventDto = new CreateEventDto
        {
            Title = "Updated Event",
            Description = "Updated Description",
            Location = "Gym B",
            LocationRoom = "Room 2",
            StartTime = DateTime.UtcNow.AddHours(3),
            EndTime = DateTime.UtcNow.AddHours(4),
        };

        var existingEvent = new EventEntity
        {
            Id = eventId,
            Title = "Original Event",
            Description = "Original Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(1),
            EndTime = DateTime.UtcNow.AddHours(2),
        };

        _mockRepository
            .Setup(r => r.GetByIdAsync(eventId))
            .ReturnsAsync(RepositoryResult<EventEntity>.Success(existingEvent));

        _mockRepository
            .Setup(r =>
                r.HasBookingConflictAsync(
                    updateEventDto.Location,
                    updateEventDto.LocationRoom,
                    updateEventDto.StartTime,
                    updateEventDto.EndTime,
                    eventId
                )
            )
            .ReturnsAsync(RepositoryResult<bool>.Success(false));

        _mockRepository
            .Setup(r => r.UpdateAsync(It.IsAny<EventEntity>()))
            .ReturnsAsync(RepositoryResult<EventEntity>.Success(existingEvent));

        // Act
        var result = await _eventService.UpdateEventAsync(eventId, updateEventDto);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Data);
        Assert.Equal(updateEventDto.Title, result.Data.Title);
    }

    [Fact]
    public async Task UpdateEventAsync_EventNotFound_ReturnsNotFoundError()
    {
        // Arrange
        var eventId = Guid.NewGuid();
        var updateEventDto = new CreateEventDto
        {
            Title = "Updated Event",
            Description = "Updated Description",
            Location = "Gym B",
            LocationRoom = "Room 2",
            StartTime = DateTime.UtcNow.AddHours(3),
            EndTime = DateTime.UtcNow.AddHours(4),
        };

        _mockRepository
            .Setup(r => r.GetByIdAsync(eventId))
            .ReturnsAsync(RepositoryResult<EventEntity>.Failure("Not found"));

        // Act
        var result = await _eventService.UpdateEventAsync(eventId, updateEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.NotFound, result.ErrorType);
        Assert.Equal("Event not found.", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateEventAsync_BookingConflict_ReturnsConflictError()
    {
        // Arrange
        var eventId = Guid.NewGuid();
        var updateEventDto = new CreateEventDto
        {
            Title = "Updated Event",
            Description = "Updated Description",
            Location = "Gym B",
            LocationRoom = "Room 2",
            StartTime = DateTime.UtcNow.AddHours(3),
            EndTime = DateTime.UtcNow.AddHours(4),
        };

        var existingEvent = new EventEntity
        {
            Id = eventId,
            Title = "Original Event",
            Description = "Original Description",
            Location = "Gym A",
            LocationRoom = "Room 1",
            StartTime = DateTime.UtcNow.AddHours(1),
            EndTime = DateTime.UtcNow.AddHours(2),
        };

        _mockRepository
            .Setup(r => r.GetByIdAsync(eventId))
            .ReturnsAsync(RepositoryResult<EventEntity>.Success(existingEvent));

        _mockRepository
            .Setup(r =>
                r.HasBookingConflictAsync(
                    updateEventDto.Location,
                    updateEventDto.LocationRoom,
                    updateEventDto.StartTime,
                    updateEventDto.EndTime,
                    eventId
                )
            )
            .ReturnsAsync(RepositoryResult<bool>.Success(true)); // Conflict exists

        // Act
        var result = await _eventService.UpdateEventAsync(eventId, updateEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.Conflict, result.ErrorType);
        Assert.Contains("already booked", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateEventAsync_EndTimeBeforeStartTime_ReturnsValidationError()
    {
        // Arrange
        var eventId = Guid.NewGuid();
        var updateEventDto = new CreateEventDto
        {
            Title = "Updated Event",
            Description = "Updated Description",
            Location = "Gym B",
            LocationRoom = "Room 2",
            StartTime = DateTime.UtcNow.AddHours(4),
            EndTime = DateTime.UtcNow.AddHours(3), // End before start
        };

        // Act
        var result = await _eventService.UpdateEventAsync(eventId, updateEventDto);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ServiceErrorType.Validation, result.ErrorType);
        Assert.Equal("End time must be after start time.", result.ErrorMessage);
    }
}
