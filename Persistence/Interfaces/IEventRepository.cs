using Persistence.Entities;
using Persistence.Models;

namespace Persistence.Interfaces;

public interface IEventRepository : IBaseRepository<EventEntity>
{
    Task<RepositoryResult<IEnumerable<EventEntity>>> GetUpcomingEventsAsync();
    Task<RepositoryResult<IEnumerable<EventEntity>>> GetEventsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate
    );
    Task<RepositoryResult<bool>> HasBookingConflictAsync(
        string location,
        string locationRoom,
        DateTime startTime,
        DateTime endTime,
        Guid? excludeEventId = null
    );
}
