using Microsoft.EntityFrameworkCore;
using Persistence.Contexts;
using Persistence.Entities;
using Persistence.Interfaces;
using Persistence.Models;

namespace Persistence.Repositories;

public class EventRepository : BaseRepository<EventEntity>, IEventRepository
{
    public EventRepository(DataContext context)
        : base(context) { }

    public async Task<RepositoryResult<IEnumerable<EventEntity>>> GetUpcomingEventsAsync()
    {
        try
        {
            var upcomingEvents = await _dbSet
                .Where(e => e.StartTime > DateTime.UtcNow)
                .OrderBy(e => e.StartTime)
                .ToListAsync();

            return RepositoryResult<IEnumerable<EventEntity>>.Success(upcomingEvents);
        }
        catch (Exception ex)
        {
            return RepositoryResult<IEnumerable<EventEntity>>.Failure(
                $"Error retrieving upcoming events: {ex.Message}"
            );
        }
    }

    public async Task<RepositoryResult<IEnumerable<EventEntity>>> GetEventsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        try
        {
            var events = await _dbSet
                .Where(e => e.StartTime >= startDate && e.EndTime <= endDate)
                .OrderBy(e => e.StartTime)
                .ToListAsync();

            return RepositoryResult<IEnumerable<EventEntity>>.Success(events);
        }
        catch (Exception ex)
        {
            return RepositoryResult<IEnumerable<EventEntity>>.Failure(
                $"Error retrieving events by date range: {ex.Message}"
            );
        }
    }

    public async Task<RepositoryResult<bool>> HasBookingConflictAsync(
        string location,
        string locationRoom,
        DateTime startTime,
        DateTime endTime,
        Guid? excludeEventId = null
    )
    {
        try
        {
            var query = _dbSet.Where(e =>
                e.Location == location
                && e.LocationRoom == locationRoom
                &&
                // Check for time overlap: events that start before the new event ends AND end after the new event starts
                e.StartTime < endTime
                && e.EndTime > startTime
            );

            // Exclude the current event when updating (to avoid self-conflict)
            if (excludeEventId.HasValue)
            {
                query = query.Where(e => e.Id != excludeEventId.Value);
            }

            var hasConflict = await query.AnyAsync();
            return RepositoryResult<bool>.Success(hasConflict);
        }
        catch (Exception ex)
        {
            return RepositoryResult<bool>.Failure(
                $"Error checking for booking conflicts: {ex.Message}"
            );
        }
    }
}
